import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, MapPin, Clock, Users, Calendar, Filter, Search, Heart } from 'lucide-react';

interface Opportunity {
  id: string;
  title: string;
  description: string;
  location: string;
  date: string;
  duration: string;
  volunteersNeeded: number;
  volunteersRegistered: number;
  category: string;
  urgency: 'low' | 'medium' | 'high';
  skills: string[];
}

const VolunteerOpportunities: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedUrgency, setSelectedUrgency] = useState('all');

  // Mock data - in a real app, this would come from an API
  const opportunities: Opportunity[] = [
    {
      id: '1',
      title: 'Emergency Response Team - Flood Relief',
      description: 'Join our rapid response team to assist flood victims with evacuation and immediate aid distribution.',
      location: 'Downtown District',
      date: '2024-01-15',
      duration: '4-6 hours',
      volunteersNeeded: 20,
      volunteersRegistered: 12,
      category: 'Emergency Response',
      urgency: 'high',
      skills: ['First Aid', 'Physical Fitness', 'Communication']
    },
    {
      id: '2',
      title: 'Community Preparedness Workshop',
      description: 'Help educate families about disaster preparedness and emergency planning.',
      location: 'Community Center',
      date: '2024-01-20',
      duration: '3 hours',
      volunteersNeeded: 8,
      volunteersRegistered: 5,
      category: 'Education',
      urgency: 'medium',
      skills: ['Public Speaking', 'Teaching']
    },
    {
      id: '3',
      title: 'Supply Distribution Center',
      description: 'Assist with organizing and distributing emergency supplies to affected families.',
      location: 'Warehouse District',
      date: '2024-01-18',
      duration: '6 hours',
      volunteersNeeded: 15,
      volunteersRegistered: 8,
      category: 'Logistics',
      urgency: 'medium',
      skills: ['Organization', 'Physical Work']
    },
    {
      id: '4',
      title: 'Shelter Support Services',
      description: 'Provide support services at emergency shelters including meal service and comfort care.',
      location: 'Emergency Shelter',
      date: '2024-01-16',
      duration: '8 hours',
      volunteersNeeded: 12,
      volunteersRegistered: 10,
      category: 'Support Services',
      urgency: 'high',
      skills: ['Food Service', 'Counseling', 'Childcare']
    },
    {
      id: '5',
      title: 'Technology Support Team',
      description: 'Help maintain communication systems and provide tech support during emergency operations.',
      location: 'Emergency Operations Center',
      date: '2024-01-22',
      duration: '12 hours',
      volunteersNeeded: 6,
      volunteersRegistered: 3,
      category: 'Technology',
      urgency: 'low',
      skills: ['IT Support', 'Communications', 'Technical Skills']
    }
  ];

  const categories = ['all', 'Emergency Response', 'Education', 'Logistics', 'Support Services', 'Technology'];
  const urgencyLevels = ['all', 'low', 'medium', 'high'];

  const filteredOpportunities = opportunities.filter(opportunity => {
    const matchesSearch = opportunity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         opportunity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         opportunity.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || opportunity.category === selectedCategory;
    const matchesUrgency = selectedUrgency === 'all' || opportunity.urgency === selectedUrgency;
    
    return matchesSearch && matchesCategory && matchesUrgency;
  });

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getProgressPercentage = (registered: number, needed: number) => {
    return Math.min((registered / needed) * 100, 100);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            to="/volunteer"
            className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft size={20} className="mr-2" />
            Back to Volunteer Page
          </Link>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Volunteer Opportunities
          </h1>
          <p className="text-lg text-gray-600">
            Find the perfect way to make a difference in your community
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search opportunities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Category Filter */}
            <div className="lg:w-48">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </option>
                ))}
              </select>
            </div>

            {/* Urgency Filter */}
            <div className="lg:w-48">
              <select
                value={selectedUrgency}
                onChange={(e) => setSelectedUrgency(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {urgencyLevels.map(urgency => (
                  <option key={urgency} value={urgency}>
                    {urgency === 'all' ? 'All Urgency Levels' : `${urgency.charAt(0).toUpperCase() + urgency.slice(1)} Priority`}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-600">
            Showing {filteredOpportunities.length} of {opportunities.length} opportunities
          </p>
        </div>

        {/* Opportunities Grid */}
        <div className="grid lg:grid-cols-2 gap-6">
          {filteredOpportunities.map((opportunity) => (
            <div key={opportunity.id} className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6">
              {/* Header */}
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {opportunity.title}
                  </h3>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                    <div className="flex items-center">
                      <MapPin size={16} className="mr-1" />
                      {opportunity.location}
                    </div>
                    <div className="flex items-center">
                      <Calendar size={16} className="mr-1" />
                      {new Date(opportunity.date).toLocaleDateString()}
                    </div>
                    <div className="flex items-center">
                      <Clock size={16} className="mr-1" />
                      {opportunity.duration}
                    </div>
                  </div>
                </div>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getUrgencyColor(opportunity.urgency)}`}>
                  {opportunity.urgency.charAt(0).toUpperCase() + opportunity.urgency.slice(1)} Priority
                </span>
              </div>

              {/* Description */}
              <p className="text-gray-600 mb-4">
                {opportunity.description}
              </p>

              {/* Skills */}
              <div className="mb-4">
                <p className="text-sm font-medium text-gray-700 mb-2">Required Skills:</p>
                <div className="flex flex-wrap gap-2">
                  {opportunity.skills.map((skill, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              {/* Volunteer Progress */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">Volunteers</span>
                  <span className="text-sm text-gray-600">
                    {opportunity.volunteersRegistered} / {opportunity.volunteersNeeded}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${getProgressPercentage(opportunity.volunteersRegistered, opportunity.volunteersNeeded)}%` }}
                  ></div>
                </div>
              </div>

              {/* Action Button */}
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">
                  Category: {opportunity.category}
                </span>
                <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  <Heart size={16} className="mr-2" />
                  Sign Up
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* No Results */}
        {filteredOpportunities.length === 0 && (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="text-gray-400" size={32} />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No opportunities found</h3>
            <p className="text-gray-600 mb-4">
              Try adjusting your search criteria or check back later for new opportunities.
            </p>
            <button
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('all');
                setSelectedUrgency('all');
              }}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Clear all filters
            </button>
          </div>
        )}

        {/* Call to Action */}
        <div className="mt-12 bg-blue-600 rounded-xl p-8 text-center text-white">
          <h2 className="text-2xl font-bold mb-4">
            Don't see the right opportunity?
          </h2>
          <p className="text-lg mb-6">
            Register as a volunteer and we'll notify you when new opportunities match your skills and interests.
          </p>
          <Link
            to="/volunteer/register"
            className="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Heart className="mr-2" size={20} />
            Register as Volunteer
          </Link>
        </div>
      </div>
    </div>
  );
};

export default VolunteerOpportunities;
