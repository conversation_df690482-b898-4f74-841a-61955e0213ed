import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Heart, Users, Clock, MapPin, ArrowRight, CheckCircle } from 'lucide-react';

const VolunteerPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Make a Difference
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              Join our community of volunteers and help save lives during disasters. 
              Your time and skills can make a real impact.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/volunteer/register"
                className="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
              >
                <Heart className="mr-2" size={20} />
                Become a Volunteer
              </Link>
              <Link
                to="/volunteer/opportunities"
                className="inline-flex items-center px-8 py-4 border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-blue-600 transition-all duration-300"
              >
                <Users className="mr-2" size={20} />
                View Opportunities
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Why Volunteer Section */}
      <div className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Volunteer With Us?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Every disaster response effort depends on dedicated volunteers. 
              Here's how you can make a meaningful impact.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Heart className="text-blue-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Save Lives</h3>
              <p className="text-gray-600">
                Your efforts directly contribute to saving lives and helping communities 
                recover from disasters faster.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="text-green-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Build Community</h3>
              <p className="text-gray-600">
                Connect with like-minded individuals and build lasting relationships 
                while serving your community.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="text-purple-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Gain Skills</h3>
              <p className="text-gray-600">
                Develop valuable emergency response skills and gain experience 
                in disaster management and community service.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Volunteer Roles Section */}
      <div className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Volunteer Opportunities
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Find the perfect way to contribute based on your skills and availability.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center mb-4">
                <MapPin className="text-blue-600 mr-3" size={24} />
                <h3 className="text-xl font-semibold text-gray-900">Field Response</h3>
              </div>
              <p className="text-gray-600 mb-4">
                Join rapid response teams to provide immediate assistance during disasters.
              </p>
              <div className="flex items-center text-sm text-gray-500">
                <Clock className="mr-2" size={16} />
                <span>Flexible hours</span>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center mb-4">
                <Users className="text-green-600 mr-3" size={24} />
                <h3 className="text-xl font-semibold text-gray-900">Community Outreach</h3>
              </div>
              <p className="text-gray-600 mb-4">
                Help educate communities about disaster preparedness and safety.
              </p>
              <div className="flex items-center text-sm text-gray-500">
                <Clock className="mr-2" size={16} />
                <span>Part-time</span>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center mb-4">
                <Heart className="text-red-600 mr-3" size={24} />
                <h3 className="text-xl font-semibold text-gray-900">Support Services</h3>
              </div>
              <p className="text-gray-600 mb-4">
                Provide emotional support and assistance to disaster victims.
              </p>
              <div className="flex items-center text-sm text-gray-500">
                <Clock className="mr-2" size={16} />
                <span>On-call basis</span>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <Link
              to="/volunteer/opportunities"
              className="inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-xl hover:bg-blue-700 transition-all duration-300 transform hover:scale-105"
            >
              View All Opportunities
              <ArrowRight className="ml-2" size={20} />
            </Link>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="py-20 bg-blue-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Make a Difference?
          </h2>
          <p className="text-xl mb-8">
            Join thousands of volunteers who are already making an impact in their communities.
          </p>
          <Link
            to="/volunteer/register"
            className="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
          >
            <Heart className="mr-2" size={20} />
            Start Your Volunteer Journey
          </Link>
        </div>
      </div>
    </div>
  );
};

export default VolunteerPage;
